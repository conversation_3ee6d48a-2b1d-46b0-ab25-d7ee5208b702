import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_text_styles.dart';
import '../../core/constants/app_constants.dart';
import '../providers/subscription_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/subscription_card.dart';
import '../widgets/features_list.dart';

class PlusScreen extends StatefulWidget {
  const PlusScreen({super.key});

  @override
  State<PlusScreen> createState() => _PlusScreenState();
}

class _PlusScreenState extends State<PlusScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  void _loadData() {
    final subscriptionProvider = context.read<SubscriptionProvider>();
    subscriptionProvider.loadSubscriptionPlans();
    subscriptionProvider.loadSubscriptionStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryBackground,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Content
            Expanded(
              child: Consumer<SubscriptionProvider>(
                builder: (context, subscriptionProvider, child) {
                  if (subscriptionProvider.hasActiveSubscription) {
                    return _buildActiveSubscriptionView(subscriptionProvider);
                  }
                  
                  return _buildSubscriptionPlansView(subscriptionProvider);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        gradient: AppColors.premiumGradient,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.workspace_premium,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConstants.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Seekho Plus',
                  style: AppTextStyles.h4.copyWith(color: Colors.white),
                ),
                Text(
                  'Unlock premium content & features',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveSubscriptionView(SubscriptionProvider provider) {
    final subscription = provider.currentSubscription!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Active Subscription Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: AppConstants.smallPadding),
                    Text(
                      'Active Subscription',
                      style: AppTextStyles.h6.copyWith(color: Colors.white),
                    ),
                  ],
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                Text(
                  subscription.plan.name,
                  style: AppTextStyles.h4.copyWith(color: Colors.white),
                ),
                const SizedBox(height: AppConstants.smallPadding),
                Text(
                  'Valid until ${_formatDate(subscription.endDate)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                if (subscription.daysRemaining > 0) ...[
                  const SizedBox(height: AppConstants.smallPadding),
                  Text(
                    '${subscription.daysRemaining} days remaining',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Features
          Text(
            'Your Premium Features',
            style: AppTextStyles.sectionTitle,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          FeaturesList(features: subscription.plan.features),
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Manage Subscription
          _buildManageSubscriptionSection(provider),
        ],
      ),
    );
  }

  Widget _buildSubscriptionPlansView(SubscriptionProvider provider) {
    if (provider.isLoadingPlans) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryAccent,
        ),
      );
    }

    if (provider.plansState == SubscriptionState.error) {
      return _buildErrorWidget(
        provider.errorMessage ?? 'Failed to load subscription plans',
        () => provider.loadSubscriptionPlans(),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trial Plan
          if (provider.trialPlan != null) ...[
            Text(
              'Start Your Journey',
              style: AppTextStyles.sectionTitle,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SubscriptionCard(
              plan: provider.trialPlan!,
              isRecommended: true,
              onSubscribe: () => _subscribeToPlan(provider.trialPlan!.id),
              isLoading: provider.isProcessingPayment,
            ),
            const SizedBox(height: AppConstants.largePadding),
          ],
          
          // Monthly Plan
          if (provider.monthlyPlan != null) ...[
            Text(
              'Continue Learning',
              style: AppTextStyles.sectionTitle,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            SubscriptionCard(
              plan: provider.monthlyPlan!,
              isRecommended: false,
              onSubscribe: () => _subscribeToPlan(provider.monthlyPlan!.id),
              isLoading: provider.isProcessingPayment,
            ),
          ],
          
          const SizedBox(height: AppConstants.largePadding),
          
          // Features
          Text(
            'What You Get',
            style: AppTextStyles.sectionTitle,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          FeaturesList(
            features: provider.plans.isNotEmpty 
                ? provider.plans.first.features 
                : _getDefaultFeatures(),
          ),
        ],
      ),
    );
  }

  Widget _buildManageSubscriptionSection(SubscriptionProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Manage Subscription',
          style: AppTextStyles.sectionTitle,
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Column(
            children: [
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: Icon(
                  Icons.cancel_outlined,
                  color: AppColors.errorColor,
                ),
                title: Text(
                  'Cancel Subscription',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.errorColor,
                  ),
                ),
                subtitle: Text(
                  'You can cancel anytime',
                  style: AppTextStyles.bodySmall,
                ),
                onTap: () => _showCancelDialog(provider),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(String message, VoidCallback onRetry) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: AppColors.errorColor,
              size: 48,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              message,
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.errorColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryAccent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _subscribeToPlan(String planId) {
    final authProvider = context.read<AuthProvider>();
    if (!authProvider.isAuthenticated) {
      // Show login dialog or navigate to login
      _showLoginRequiredDialog();
      return;
    }

    final subscriptionProvider = context.read<SubscriptionProvider>();
    subscriptionProvider.subscribeToPlan(planId);
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        title: Text(
          'Login Required',
          style: AppTextStyles.h6,
        ),
        content: Text(
          'Please login to subscribe to premium plans.',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.secondaryText,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to login or trigger login
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryAccent,
            ),
            child: Text(
              'Login',
              style: AppTextStyles.buttonMedium.copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showCancelDialog(SubscriptionProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        title: Text(
          'Cancel Subscription',
          style: AppTextStyles.h6,
        ),
        content: Text(
          'Are you sure you want to cancel your subscription? You will lose access to premium features.',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Keep Subscription',
              style: AppTextStyles.buttonMedium.copyWith(
                color: AppColors.primaryAccent,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              provider.cancelSubscription();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
            ),
            child: Text(
              'Cancel',
              style: AppTextStyles.buttonMedium.copyWith(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  List<String> _getDefaultFeatures() {
    return [
      'Access to all premium videos',
      'Download videos for offline viewing',
      'Ad-free experience',
      'Priority customer support',
      'Early access to new content',
    ];
  }
}
