import 'package:flutter/foundation.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../../domain/entities/subscription.dart';
import '../../domain/usecases/subscription/get_subscription_plans_usecase.dart';
import '../../domain/usecases/subscription/create_order_usecase.dart';
import '../../domain/usecases/subscription/verify_payment_usecase.dart';
import '../../domain/usecases/subscription/get_subscription_status_usecase.dart';
import '../../domain/usecases/subscription/cancel_subscription_usecase.dart';
import '../../core/error/failures.dart';

enum SubscriptionState { initial, loading, loaded, error }
enum PaymentState { initial, processing, success, failed }

class SubscriptionProvider extends ChangeNotifier {
  final GetSubscriptionPlansUseCase getSubscriptionPlansUseCase;
  final CreateOrderUseCase createOrderUseCase;
  final VerifyPaymentUseCase verifyPaymentUseCase;
  final GetSubscriptionStatusUseCase getSubscriptionStatusUseCase;
  final CancelSubscriptionUseCase cancelSubscriptionUseCase;

  late Razorpay _razorpay;

  SubscriptionProvider({
    required this.getSubscriptionPlansUseCase,
    required this.createOrderUseCase,
    required this.verifyPaymentUseCase,
    required this.getSubscriptionStatusUseCase,
    required this.cancelSubscriptionUseCase,
  }) {
    _initializeRazorpay();
  }

  SubscriptionState _plansState = SubscriptionState.initial;
  SubscriptionState _statusState = SubscriptionState.initial;
  PaymentState _paymentState = PaymentState.initial;

  List<SubscriptionPlan> _plans = [];
  UserSubscription? _currentSubscription;
  String? _errorMessage;
  PaymentOrder? _currentOrder;

  // Getters
  SubscriptionState get plansState => _plansState;
  SubscriptionState get statusState => _statusState;
  PaymentState get paymentState => _paymentState;
  
  List<SubscriptionPlan> get plans => _plans;
  UserSubscription? get currentSubscription => _currentSubscription;
  String? get errorMessage => _errorMessage;
  
  bool get hasActiveSubscription => _currentSubscription?.isActive == true;
  bool get isLoadingPlans => _plansState == SubscriptionState.loading;
  bool get isLoadingStatus => _statusState == SubscriptionState.loading;
  bool get isProcessingPayment => _paymentState == PaymentState.processing;

  SubscriptionPlan? get trialPlan => _plans.where((plan) => plan.isTrial).firstOrNull;
  SubscriptionPlan? get monthlyPlan => _plans.where((plan) => !plan.isTrial).firstOrNull;

  void _initializeRazorpay() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  @override
  void dispose() {
    _razorpay.clear();
    super.dispose();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  Future<void> loadSubscriptionPlans() async {
    _plansState = SubscriptionState.loading;
    notifyListeners();

    final result = await getSubscriptionPlansUseCase.call();

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
        _plansState = SubscriptionState.error;
      },
      (plans) {
        _plans = plans;
        _plansState = SubscriptionState.loaded;
      },
    );
    notifyListeners();
  }

  Future<void> loadSubscriptionStatus() async {
    _statusState = SubscriptionState.loading;
    notifyListeners();

    final result = await getSubscriptionStatusUseCase.call();

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
        _statusState = SubscriptionState.error;
      },
      (subscription) {
        _currentSubscription = subscription;
        _statusState = SubscriptionState.loaded;
      },
    );
    notifyListeners();
  }

  Future<void> subscribeToPlan(String planId) async {
    _paymentState = PaymentState.processing;
    notifyListeners();

    final result = await createOrderUseCase.call(CreateOrderParams(planId: planId));

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
        _paymentState = PaymentState.failed;
        notifyListeners();
      },
      (order) {
        _currentOrder = order;
        _openRazorpayCheckout(order);
      },
    );
  }

  void _openRazorpayCheckout(PaymentOrder order) {
    final plan = _plans.firstWhere((p) => p.id == order.planId);
    
    var options = {
      'key': 'YOUR_RAZORPAY_KEY_ID', // Replace with actual key
      'amount': order.amount,
      'name': 'Seekho',
      'description': plan.name,
      'order_id': order.razorpayOrderId,
      'prefill': {
        'contact': '',
        'email': '',
      },
      'theme': {
        'color': '#6C63FF',
      }
    };

    try {
      _razorpay.open(options);
    } catch (e) {
      _errorMessage = 'Failed to open payment gateway';
      _paymentState = PaymentState.failed;
      notifyListeners();
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    if (_currentOrder == null) return;

    final result = await verifyPaymentUseCase.call(VerifyPaymentParams(
      orderId: _currentOrder!.id,
      paymentId: response.paymentId!,
      signature: response.signature!,
    ));

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
        _paymentState = PaymentState.failed;
      },
      (subscription) {
        _currentSubscription = subscription;
        _paymentState = PaymentState.success;
        _currentOrder = null;
      },
    );
    notifyListeners();
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    _errorMessage = response.message ?? 'Payment failed';
    _paymentState = PaymentState.failed;
    _currentOrder = null;
    notifyListeners();
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Handle external wallet payment
  }

  Future<void> cancelSubscription() async {
    final result = await cancelSubscriptionUseCase.call();

    result.fold(
      (failure) {
        _errorMessage = _mapFailureToMessage(failure);
      },
      (_) {
        _currentSubscription = _currentSubscription?.copyWith(
          isActive: false,
          status: 'cancelled',
        );
      },
    );
    notifyListeners();
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case PaymentFailure:
        return failure.message;
      case SubscriptionFailure:
        return failure.message;
      case NetworkFailure:
        return 'Network error. Please check your internet connection.';
      case ServerFailure:
        return 'Server error. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}
